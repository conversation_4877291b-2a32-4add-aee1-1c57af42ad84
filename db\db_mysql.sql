CREATE TABLE `tbl_addons` (
  `AddonID` int(11) NOT NULL,
  `AddonName` varchar(100) NOT NULL,
  `Price` decimal(10,2) NOT NULL,
  `IsActive` tinyint(1) DEFAULT '1',
  `Code` varchar(20) NOT NULL,
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `tbl_addons` (`AddonID`, `AddonName`, `Price`, `IsActive`, `Code`, `CreatedAt`, `UpdatedAt`, `IsDeleted`) VALUES
(1, 'ملح', '0.00', 1, 'ADD001000000', '2025-08-07 09:02:06', '2025-08-07 09:02:06', 0);

CREATE TABLE `tbl_cash_drawers` (
  `CashDrawerID` int(11) NOT NULL,
  `DrawerName` varchar(50) NOT NULL,
  `Location` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `tbl_cash_drawers` (`CashDrawerID`, `DrawerName`, `Location`) VALUES
(1, 'الدرج الرئيسي', 'الفرع الرئيسي'),
(2, 'درج المحاسب', 'الفرع 2');

CREATE TABLE `tbl_cash_management` (
  `TransactionID` int(11) NOT NULL,
  `UserID` int(11) NOT NULL,
  `TransactionType` varchar(20) NOT NULL,
  `Amount` decimal(10,2) NOT NULL,
  `Description` varchar(200) DEFAULT NULL,
  `TransactionDate` datetime DEFAULT CURRENT_TIMESTAMP,
  `IsClosed` tinyint(1) DEFAULT '0',
  `ReferenceID` int(11) DEFAULT NULL,
  `ReferenceType` varchar(50) DEFAULT NULL,
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `tbl_categories` (
  `CategoryID` int(11) NOT NULL,
  `CategoryCode` varchar(15) DEFAULT NULL,
  `CategoryName` varchar(100) NOT NULL,
  `PrinterName` varchar(250) DEFAULT NULL,
  `Images` blob,
  `IsActive` tinyint(1) NOT NULL DEFAULT '1',
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `tbl_categories` (`CategoryID`, `CategoryCode`, `CategoryName`, `PrinterName`, `Images`, `IsActive`, `CreatedAt`, `UpdatedAt`, `IsDeleted`) VALUES
(1, 'CAT100001006', 'All Category', 'OneNote for Windows 10', NULL, 1, '2025-08-07 09:02:06', '2025-08-07 09:02:06', 0);

CREATE TABLE `tbl_codes` (
  `UNIQUEID` int(11) NOT NULL,
  `TBL_NAME` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `Prefix` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ValueGeneratedLast` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `tbl_codes` (`UNIQUEID`, `TBL_NAME`, `Prefix`, `ValueGeneratedLast`) VALUES
(1, '2', 'FAM', 1),
(2, '2', 'FAMS', 2),
(3, '2', 'FAMSS', 100),
(10, 'Categories', 'CAT1', 1008),
(11, 'Addons', 'ADD', 1000002),
(12, 'ExpensesTypes', 'Expenses', 102),
(13, 'Products', 'ART-', 1000);

CREATE TABLE `tbl_customers` (
  `CustomerID` int(11) NOT NULL,
  `FullName` varchar(100) NOT NULL,
  `Phone` varchar(20) DEFAULT NULL,
  `Email` varchar(100) DEFAULT NULL,
  `Address` varchar(200) DEFAULT NULL,
  `LoyaltyPoints` int(11) DEFAULT '0',
  `IsActive` tinyint(1) NOT NULL DEFAULT '1',
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0',
  `CreditLimit` decimal(10,2) DEFAULT '0.00',
  `Balance` decimal(10,2) DEFAULT '0.00'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `tbl_customers` (`CustomerID`, `FullName`, `Phone`, `Email`, `Address`, `LoyaltyPoints`, `IsActive`, `CreatedAt`, `UpdatedAt`, `IsDeleted`, `CreditLimit`, `Balance`) VALUES
(1, 'Alice Johnson', '1234567890', '<EMAIL>', '123 Main St', 0, 1, '2025-08-07 09:09:33', '2025-08-07 09:16:19', 0, '500.00', '0.00'),
(2, 'Bob Smith', '9876543210', '<EMAIL>', '456 Side St', 0, 1, '2025-08-07 09:09:33', '2025-08-07 09:09:33', 0, '300.00', '0.00');

CREATE TABLE `tbl_customer_payments` (
  `PaymentID` int(11) NOT NULL,
  `OrderID` int(11) DEFAULT NULL,
  `CustomerID` int(11) DEFAULT NULL,
  `PaymentMethodID` int(11) NOT NULL,
  `Amount` decimal(10,2) NOT NULL,
  `CashDrawerID` int(11) DEFAULT NULL,
  `ReceivedByUserID` int(11) NOT NULL,
  `PaymentDate` datetime DEFAULT CURRENT_TIMESTAMP,
  `Notes` varchar(255) DEFAULT NULL,
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `tbl_customer_payments` (`PaymentID`, `OrderID`, `CustomerID`, `PaymentMethodID`, `Amount`, `CashDrawerID`, `ReceivedByUserID`, `PaymentDate`, `Notes`, `CreatedAt`, `UpdatedAt`, `IsDeleted`) VALUES
(3, NULL, 1, 1, '100.00', NULL, 1, '2025-08-07 09:12:35', NULL, '2025-08-07 09:12:35', '2025-08-07 09:12:35', 0),
(4, NULL, 1, 1, '100.00', NULL, 1, '2025-08-07 09:15:52', NULL, '2025-08-07 09:15:52', '2025-08-07 09:15:52', 0),
(5, NULL, 1, 1, '-100.00', NULL, 1, '2025-08-07 09:16:09', NULL, '2025-08-07 09:16:09', '2025-08-07 09:16:09', 0),
(6, NULL, 1, 1, '-100.00', NULL, 1, '2025-08-07 09:16:19', NULL, '2025-08-07 09:16:19', '2025-08-07 09:16:19', 0);
DELIMITER $$
CREATE TRIGGER `trg_after_payment_insert` AFTER INSERT ON `tbl_customer_payments` FOR EACH ROW BEGIN
  IF NEW.IsDeleted = 0 THEN
    UPDATE tbl_customers
    SET Balance = Balance - NEW.Amount
    WHERE CustomerID = NEW.CustomerID;
  END IF;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `trg_after_payment_update` AFTER UPDATE ON `tbl_customer_payments` FOR EACH ROW BEGIN
  IF OLD.IsDeleted = 0 AND NEW.IsDeleted = 0 THEN
    UPDATE tbl_customers
    SET Balance = Balance + (OLD.Amount - NEW.Amount)
    WHERE CustomerID = NEW.CustomerID;
  ELSEIF OLD.IsDeleted = 0 AND NEW.IsDeleted = 1 THEN
    UPDATE tbl_customers
    SET Balance = Balance + OLD.Amount
    WHERE CustomerID = OLD.CustomerID;
  ELSEIF OLD.IsDeleted = 1 AND NEW.IsDeleted = 0 THEN
    UPDATE tbl_customers
    SET Balance = Balance - NEW.Amount
    WHERE CustomerID = NEW.CustomerID;
  END IF;
END
$$
DELIMITER ;

CREATE TABLE `tbl_expenses` (
  `ExpenseID` int(11) NOT NULL,
  `ExpenseDate` datetime DEFAULT CURRENT_TIMESTAMP,
  `ExpenseTypeID` int(11) NOT NULL,
  `Amount` decimal(10,2) NOT NULL,
  `Description` varchar(200) DEFAULT NULL,
  `UserID` int(11) NOT NULL,
  `CashDrawerID` int(11) NOT NULL,
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DELIMITER $$
CREATE TRIGGER `trg_after_delete_expense` AFTER DELETE ON `tbl_expenses` FOR EACH ROW BEGIN
  DELETE FROM tbl_cash_management
  WHERE ReferenceID = OLD.ExpenseID
    AND ReferenceType = 'Expense';
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `trg_after_insert_expense` AFTER INSERT ON `tbl_expenses` FOR EACH ROW BEGIN
  INSERT INTO tbl_cash_management (
    UserID,
    TransactionType,
    Amount,
    Description,
    TransactionDate,
    ReferenceID,
    ReferenceType
  )
  VALUES (
    NEW.UserID,
    'Expense',
    NEW.Amount,
    NEW.Description,
    NEW.ExpenseDate,
    NEW.ExpenseID,
    'Expense'
  );
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `trg_after_update_expense` AFTER UPDATE ON `tbl_expenses` FOR EACH ROW BEGIN
  UPDATE tbl_cash_management
  SET
    Amount = NEW.Amount,
    Description = NEW.Description,
    TransactionDate = NEW.ExpenseDate,
    UpdatedAt = CURRENT_TIMESTAMP
  WHERE
    ReferenceID = NEW.ExpenseID
    AND ReferenceType = 'Expense'
    AND IsDeleted = 0;
END
$$
DELIMITER ;

CREATE TABLE `tbl_expense_types` (
  `ExpenseTypeID` int(11) NOT NULL,
  `Code` varchar(20) DEFAULT NULL,
  `TypeName` varchar(100) NOT NULL,
  `Note` varchar(255) DEFAULT NULL,
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE `tbl_ingredients` (
  `IngredientID` int(11) NOT NULL,
  `Name` varchar(100) NOT NULL,
  `Unit` varchar(20) NOT NULL,
  `UnitCost` decimal(10,2) NOT NULL DEFAULT '0.00',
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE `tbl_inventory` (
  `InventoryID` int(11) NOT NULL,
  `ProductID` int(11) NOT NULL,
  `Quantity` decimal(10,2) NOT NULL,
  `MovementType` varchar(20) NOT NULL,
  `UnitCost` decimal(10,2) DEFAULT NULL,
  `ReferenceID` int(11) DEFAULT NULL,
  `ReferenceType` varchar(50) DEFAULT NULL,
  `ExpiryDate` date DEFAULT NULL,
  `MovementDate` datetime DEFAULT CURRENT_TIMESTAMP,
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `tbl_orders` (
  `OrderID` int(11) NOT NULL,
  `SessionID` int(11) DEFAULT NULL,
  `CustomerID` int(11) DEFAULT NULL,
  `OrderType` varchar(20) NOT NULL,
  `TotalAmount` decimal(10,2) NOT NULL,
  `OrderDate` datetime DEFAULT CURRENT_TIMESTAMP,
  `UserID` int(11) NOT NULL,
  `Status` varchar(20) DEFAULT 'PENDING',
  `PaymentMethodID` int(11) DEFAULT NULL,
  `PaymentStatus` varchar(20) DEFAULT 'UNPAID',
  `Notes` varchar(500) DEFAULT NULL,
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
DELIMITER $$
CREATE TRIGGER `trg_after_order_insert` AFTER INSERT ON `tbl_orders` FOR EACH ROW BEGIN
  IF NEW.IsDeleted = 0 THEN
    UPDATE tbl_customers
    SET Balance = Balance + NEW.TotalAmount
    WHERE CustomerID = NEW.CustomerID;
  END IF;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `trg_after_order_update` AFTER UPDATE ON `tbl_orders` FOR EACH ROW BEGIN
  IF OLD.IsDeleted = 0 AND NEW.IsDeleted = 0 THEN
    UPDATE tbl_customers
    SET Balance = Balance + (NEW.TotalAmount - OLD.TotalAmount)
    WHERE CustomerID = NEW.CustomerID;
  ELSEIF OLD.IsDeleted = 0 AND NEW.IsDeleted = 1 THEN
    UPDATE tbl_customers
    SET Balance = Balance - OLD.TotalAmount
    WHERE CustomerID = OLD.CustomerID;
  ELSEIF OLD.IsDeleted = 1 AND NEW.IsDeleted = 0 THEN
    UPDATE tbl_customers
    SET Balance = Balance + NEW.TotalAmount
    WHERE CustomerID = NEW.CustomerID;
  END IF;
END
$$
DELIMITER ;

CREATE TABLE `tbl_order_addons` (
  `OrderDetailAddonID` int(11) NOT NULL,
  `OrderDetailID` int(11) NOT NULL,
  `AddonID` int(11) NOT NULL,
  `Price` decimal(10,2) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `tbl_order_details` (
  `OrderDetailID` int(11) NOT NULL,
  `OrderID` int(11) NOT NULL,
  `ProductID` int(11) NOT NULL,
  `Quantity` int(11) NOT NULL,
  `UnitPrice` decimal(10,2) NOT NULL,
  `ProductSizeID` int(11) DEFAULT NULL,
  `ExtraAddonTotal` decimal(10,2) DEFAULT '0.00',
  `AddonIDs` text,
  `AddonNames` text,
  `AddonPrices` text,
  `KitchenStatus` varchar(20) DEFAULT 'PENDING',
  `KitchenNote` varchar(200) DEFAULT NULL,
  `KitchenTimestamp` datetime DEFAULT NULL,
  `SpecialInstructions` varchar(500) DEFAULT NULL,
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `tbl_payment_methods` (
  `PaymentMethodID` int(11) NOT NULL,
  `MethodName` varchar(50) NOT NULL,
  `IsActive` tinyint(1) DEFAULT '1',
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `tbl_payment_methods` (`PaymentMethodID`, `MethodName`, `IsActive`, `CreatedAt`, `UpdatedAt`, `IsDeleted`) VALUES
(1, 'Cash', 1, '2025-08-07 09:12:30', '2025-08-07 09:12:30', 0);

CREATE TABLE `tbl_products` (
  `ProductID` int(11) NOT NULL,
  `ProductName` varchar(100) NOT NULL,
  `Code` varchar(50) DEFAULT NULL,
  `CategoryID` int(11) NOT NULL,
  `IsStockable` tinyint(1) DEFAULT '1',
  `Price` decimal(10,2) NOT NULL,
  `CostPrice` decimal(10,2) DEFAULT '0.00',
  `IsActive` tinyint(1) DEFAULT '1',
  `Image` longblob,
  `Note` text,
  `ImageURL` varchar(255) DEFAULT NULL,
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `tbl_products` (`ProductID`, `ProductName`, `Code`, `CategoryID`, `IsStockable`, `Price`, `CostPrice`, `IsActive`, `Image`, `Note`, `ImageURL`, `CreatedAt`, `UpdatedAt`, `IsDeleted`) VALUES
(3, 'Pizza', NULL, 1, 0, '180.00', '145.00', 1, NULL, NULL, NULL, '2025-08-07 09:02:04', '2025-08-07 09:02:04', 0);

CREATE TABLE `tbl_product_ingredients` (
  `ProductIngredientID` int(11) NOT NULL,
  `ProductID` int(11) NOT NULL,
  `IngredientID` int(11) NOT NULL,
  `QuantityUsed` decimal(10,2) NOT NULL,
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE `tbl_product_sizes` (
  `ProductSizeID` int(11) NOT NULL,
  `ProductID` int(11) NOT NULL,
  `SizeName` varchar(50) NOT NULL,
  `ExtraPrice` decimal(10,2) NOT NULL,
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `tbl_purchases` (
  `PurchaseID` int(11) NOT NULL,
  `SupplierID` int(11) DEFAULT NULL,
  `PurchaseDate` datetime DEFAULT CURRENT_TIMESTAMP,
  `TotalAmount` decimal(10,2) DEFAULT NULL,
  `SubTotal` decimal(10,2) DEFAULT '0.00',
  `Discount` decimal(10,2) DEFAULT '0.00',
  `Tax` decimal(10,2) DEFAULT '0.00',
  `GrandTotal` decimal(10,2) DEFAULT '0.00',
  `PaymentStatus` varchar(20) DEFAULT 'UNPAID',
  `Notes` varchar(255) DEFAULT NULL,
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `tbl_purchase_details` (
  `PurchaseDetailID` int(11) NOT NULL,
  `PurchaseID` int(11) NOT NULL,
  `ProductID` int(11) NOT NULL,
  `Quantity` decimal(10,2) NOT NULL,
  `UnitPrice` decimal(10,2) NOT NULL,
  `ExpiryDate` date DEFAULT NULL,
  `Total` decimal(10,2) GENERATED ALWAYS AS ((`Quantity` * `UnitPrice`)) STORED,
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `tbl_purchase_payments` (
  `PaymentID` int(11) NOT NULL,
  `PurchaseID` int(11) NOT NULL,
  `AmountPaid` decimal(10,2) NOT NULL,
  `PaymentDate` datetime DEFAULT CURRENT_TIMESTAMP,
  `PaymentMethod` varchar(50) DEFAULT NULL,
  `Notes` varchar(255) DEFAULT NULL,
  `UserID` int(11) DEFAULT NULL,
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
DELIMITER $$
CREATE TRIGGER `trg_update_purchase_payment_status` AFTER INSERT ON `tbl_purchase_payments` FOR EACH ROW BEGIN
  DECLARE total_paid DECIMAL(10,2);
  DECLARE total_due DECIMAL(10,2);

  -- Get total paid so far for the same PurchaseID
  SELECT IFNULL(SUM(AmountPaid), 0)
  INTO total_paid
  FROM tbl_purchase_payments
  WHERE PurchaseID = NEW.PurchaseID;

  -- Get the total amount due from the purchases table
  SELECT GrandTotal
  INTO total_due
  FROM tbl_purchases
  WHERE PurchaseID = NEW.PurchaseID;

  -- Update payment status
  IF total_paid = 0 THEN
    UPDATE tbl_purchases
    SET PaymentStatus = 'UNPAID'
    WHERE PurchaseID = NEW.PurchaseID;

  ELSEIF total_paid < total_due THEN
    UPDATE tbl_purchases
    SET PaymentStatus = 'PARTIAL'
    WHERE PurchaseID = NEW.PurchaseID;

  ELSE
    UPDATE tbl_purchases
    SET PaymentStatus = 'PAID'
    WHERE PurchaseID = NEW.PurchaseID;
  END IF;
END
$$
DELIMITER ;

CREATE TABLE `tbl_roles` (
  `RoleID` int(11) NOT NULL,
  `RoleName` varchar(50) NOT NULL,
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `tbl_roles` (`RoleID`, `RoleName`, `CreatedAt`, `UpdatedAt`, `IsDeleted`) VALUES
(1, 'Administrator', '2025-08-07 09:02:05', '2025-08-07 09:02:05', 0),
(2, 'Manager', '2025-08-07 09:02:05', '2025-08-07 09:02:05', 0),
(3, 'Cashier', '2025-08-07 09:02:05', '2025-08-07 09:02:05', 0),
(4, 'Waiter', '2025-08-07 09:02:05', '2025-08-07 09:02:05', 0),
(5, 'Kitchen Staff', '2025-08-07 09:02:05', '2025-08-07 09:02:05', 0),
(6, 'مدير النظام', '2025-08-07 09:02:05', '2025-08-07 09:02:05', 0),
(7, 'مدير المطعم', '2025-08-07 09:02:05', '2025-08-07 09:02:05', 0),
(8, 'نقدي', '2025-08-07 09:02:05', '2025-08-07 09:02:05', 0),
(9, 'نادل', '2025-08-07 09:02:05', '2025-08-07 09:02:05', 0),
(10, 'طاقم المطبخ', '2025-08-07 09:02:05', '2025-08-07 09:02:05', 0),
(11, 'مشرف المخزن', '2025-08-07 09:02:05', '2025-08-07 09:02:05', 0);

CREATE TABLE `tbl_role_permissions` (
  `PermissionID` int(11) NOT NULL,
  `RoleID` int(11) NOT NULL,
  `ModuleName` varchar(100) NOT NULL,
  `CanView` tinyint(1) DEFAULT '0',
  `CanAdd` tinyint(1) DEFAULT '0',
  `CanEdit` tinyint(1) DEFAULT '0',
  `CanDelete` tinyint(1) DEFAULT '0',
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `tbl_sessions` (
  `SessionID` int(11) NOT NULL,
  `TableID` int(11) DEFAULT NULL,
  `StartTime` datetime DEFAULT CURRENT_TIMESTAMP,
  `EndTime` datetime DEFAULT NULL,
  `IsClosed` tinyint(1) DEFAULT '0',
  `SessionName` varchar(50) DEFAULT NULL,
  `UserID` int(11) NOT NULL,
  `CustomerID` int(11) DEFAULT NULL,
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `tbl_stock_movements` (
  `id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `movement_type` enum('IN','OUT') NOT NULL,
  `quantity` decimal(10,2) NOT NULL,
  `movement_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `reference` varchar(100) DEFAULT NULL,
  `note` text,
  `created_by` int(11) DEFAULT NULL,
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `tbl_suppliers` (
  `SupplierID` int(11) NOT NULL,
  `SupplierName` varchar(100) NOT NULL,
  `Phone` varchar(20) DEFAULT NULL,
  `Address` varchar(200) DEFAULT NULL,
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `tbl_tables` (
  `TableID` int(11) NOT NULL,
  `TableName` varchar(50) NOT NULL,
  `IsOccupied` tinyint(1) DEFAULT '0',
  `Capacity` int(11) DEFAULT '4',
  `IsActive` tinyint(1) NOT NULL DEFAULT '1',
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `tbl_users` (
  `UserID` int(11) NOT NULL,
  `FullName` varchar(100) NOT NULL,
  `Username` varchar(50) NOT NULL,
  `PasswordHash` varchar(255) NOT NULL,
  `RoleID` int(11) NOT NULL,
  `IsActive` tinyint(1) DEFAULT '1',
  `JobTitle` varchar(100) DEFAULT NULL,
  `Phone` varchar(20) DEFAULT NULL,
  `SalaryType` varchar(20) DEFAULT NULL,
  `SalaryValue` decimal(10,2) DEFAULT NULL,
  `CreatedAt` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `IsDeleted` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `tbl_users` (`UserID`, `FullName`, `Username`, `PasswordHash`, `RoleID`, `IsActive`, `JobTitle`, `Phone`, `SalaryType`, `SalaryValue`, `CreatedAt`, `UpdatedAt`, `IsDeleted`) VALUES
(1, 'أحمد محمد', 'ahmed', 'n5lZwhV4gF3XTbP56YTOBl1J4LU9mL5mgG0mznmZU37TjKqw', 1, 1, 'مدير النظام', '0501234567', 'Monthly', '15000.00', '2025-08-07 09:02:05', '2025-08-07 12:35:03', 0),
(2, 'سارة عبدالله', 'sara', 'n5lZwhV4gF3XTbP56YTOBl1J4LU9mL5mgG0mznmZU37TjKqw', 2, 1, 'مديرة المطعم', '0512345678', 'Monthly', '12000.00', '2025-08-07 09:02:05', '2025-08-07 12:35:03', 0),
(3, 'خالد علي', 'khaled', 'n5lZwhV4gF3XTbP56YTOBl1J4LU9mL5mgG0mznmZU37TjKqw', 3, 1, 'موظف نقدي', '0523456789', 'Monthly', '8000.00', '2025-08-07 09:02:05', '2025-08-07 12:35:03', 0),
(4, 'لمى أحمد', 'lama', 'n5lZwhV4gF3XTbP56YTOBl1J4LU9mL5mgG0mznmZU37TjKqw', 4, 1, 'نادلة', '0534567890', 'Daily', '300.00', '2025-08-07 09:02:05', '2025-08-07 12:35:03', 0),
(5, 'يوسف إبراهيم', 'yousef', 'n5lZwhV4gF3XTbP56YTOBl1J4LU9mL5mgG0mznmZU37TjKqw', 5, 1, 'طاهي', '0545678901', 'Monthly', '9000.00', '2025-08-07 09:02:05', '2025-08-07 12:35:03', 0);

CREATE TABLE `tbl_user_activities` (
  `ActivityID` int(11) NOT NULL,
  `UserID` int(11) NOT NULL,
  `ActivityType` varchar(50) NOT NULL,
  `Description` varchar(255) DEFAULT NULL,
  `IPAddress` varchar(50) DEFAULT NULL,
  `ActivityDate` datetime DEFAULT CURRENT_TIMESTAMP,
  `UpdatedAt` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `tbl_user_activities` (`ActivityID`, `UserID`, `ActivityType`, `Description`, `IPAddress`, `ActivityDate`, `UpdatedAt`) VALUES
(1, 1, 'FAILED_LOGIN', 'Failed login attempt from IP: ************', NULL, '2025-08-07 12:07:17', '2025-08-07 12:07:17'),
(2, 1, 'FAILED_LOGIN', 'Failed login attempt from IP: ************', NULL, '2025-08-07 12:07:23', '2025-08-07 12:07:23'),
(3, 1, 'LOGIN', 'Successful login from IP: ************', NULL, '2025-08-07 12:35:11', '2025-08-07 12:35:11'),
(4, 1, 'LOGIN', 'Successful login from IP: ************', NULL, '2025-08-07 12:35:33', '2025-08-07 12:35:33'),
(5, 1, 'LOGIN', 'Successful login from IP: ************', NULL, '2025-08-07 12:36:54', '2025-08-07 12:36:54'),
(6, 1, 'MAIN_FORM_LOADED', 'Main form loaded successfully', NULL, '2025-08-07 12:36:55', '2025-08-07 12:36:55'),
(7, 1, 'LOGIN', 'Successful login from IP: ************', NULL, '2025-08-07 12:37:16', '2025-08-07 12:37:16'),
(8, 1, 'MAIN_FORM_LOADED', 'Main form loaded successfully', NULL, '2025-08-07 12:37:17', '2025-08-07 12:37:17'),
(9, 1, 'POS_OPENED', 'POS Terminal opened', NULL, '2025-08-07 12:37:24', '2025-08-07 12:37:24'),
(10, 1, 'FORM_OPENED', 'Opened Daily Sales Report form', NULL, '2025-08-07 12:37:29', '2025-08-07 12:37:29'),
(11, 1, 'FORM_OPENED', 'Opened Cash Management form', NULL, '2025-08-07 12:37:30', '2025-08-07 12:37:30'),
(12, 1, 'KITCHEN_OPENED', 'Kitchen Display opened', NULL, '2025-08-07 12:37:30', '2025-08-07 12:37:30'),
(13, 1, 'POS_OPENED', 'POS Terminal opened', NULL, '2025-08-07 12:37:31', '2025-08-07 12:37:31'),
(14, 1, 'FORM_OPENED', 'Opened Customer Management form', NULL, '2025-08-07 12:37:33', '2025-08-07 12:37:33'),
(15, 1, 'KITCHEN_OPENED', 'Kitchen Display opened', NULL, '2025-08-07 12:37:39', '2025-08-07 12:37:39'),
(16, 1, 'LOGOUT', 'User logged out', NULL, '2025-08-07 12:37:48', '2025-08-07 12:37:48'),
(17, 1, 'LOGIN', 'Successful login from IP: ************', NULL, '2025-08-07 12:52:29', '2025-08-07 12:52:29'),
(18, 1, 'MAIN_FORM_LOADED', 'Main form loaded successfully', NULL, '2025-08-07 12:52:30', '2025-08-07 12:52:30'),
(19, 1, 'APPLICATION_EXIT', 'User exited the application', NULL, '2025-08-07 12:52:37', '2025-08-07 12:52:37'),
(20, 1, 'LOGIN', 'Successful login from IP: ************', NULL, '2025-08-07 12:53:46', '2025-08-07 12:53:46'),
(21, 1, 'MAIN_FORM_LOADED', 'Main form loaded successfully', NULL, '2025-08-07 12:53:47', '2025-08-07 12:53:47'),
(22, 1, 'APPLICATION_EXIT', 'User exited the application', NULL, '2025-08-07 12:53:50', '2025-08-07 12:53:50'),
(23, 1, 'LOGIN', 'Successful login from IP: ************', NULL, '2025-08-07 12:54:19', '2025-08-07 12:54:19'),
(24, 1, 'MAIN_FORM_LOADED', 'Main form loaded successfully', NULL, '2025-08-07 12:54:19', '2025-08-07 12:54:19'),
(25, 1, 'APPLICATION_EXIT', 'User exited the application', NULL, '2025-08-07 12:54:23', '2025-08-07 12:54:23'),
(26, 1, 'LOGIN', 'Successful login from IP: ************', NULL, '2025-08-07 12:55:15', '2025-08-07 12:55:15'),
(27, 1, 'MAIN_FORM_LOADED', 'Main form loaded successfully', NULL, '2025-08-07 12:55:16', '2025-08-07 12:55:16'),
(28, 1, 'APPLICATION_EXIT', 'User exited the application', NULL, '2025-08-07 12:55:19', '2025-08-07 12:55:19'),
(29, 1, 'LOGIN', 'Successful login from IP: ************', NULL, '2025-08-07 23:26:03', '2025-08-07 23:26:03'),
(30, 1, 'MAIN_FORM_LOADED', 'Main form loaded successfully', NULL, '2025-08-07 23:26:04', '2025-08-07 23:26:04'),
(31, 1, 'APPLICATION_EXIT', 'User exited the application', NULL, '2025-08-07 23:26:48', '2025-08-07 23:26:48'),
(32, 1, 'LOGIN', 'Successful login from IP: ************', NULL, '2025-08-08 01:08:15', '2025-08-08 01:08:15'),
(33, 1, 'MAIN_FORM_LOADED', 'Main form loaded successfully', NULL, '2025-08-08 01:08:16', '2025-08-08 01:08:16'),
(34, 1, 'ORDER_HISTORY_OPENED', 'Order History opened', NULL, '2025-08-08 01:08:22', '2025-08-08 01:08:22'),
(35, 1, 'APPLICATION_EXIT', 'User exited the application', NULL, '2025-08-08 01:08:28', '2025-08-08 01:08:28'),
(36, 1, 'LOGIN', 'Successful login from IP: ************', NULL, '2025-08-08 01:09:43', '2025-08-08 01:09:43'),
(37, 1, 'MAIN_FORM_LOADED', 'Main form loaded successfully', NULL, '2025-08-08 01:09:44', '2025-08-08 01:09:44'),
(38, 1, 'PRODUCT_MGMT_OPENED', 'Product Management opened', NULL, '2025-08-08 01:09:46', '2025-08-08 01:09:46'),
(39, 1, 'APPLICATION_EXIT', 'User exited the application', NULL, '2025-08-08 01:10:28', '2025-08-08 01:10:28'),
(40, 1, 'LOGIN', 'Successful login from IP: ************', NULL, '2025-08-08 02:07:07', '2025-08-08 02:07:07'),
(41, 1, 'MAIN_FORM_LOADED', 'Main form loaded successfully', NULL, '2025-08-08 02:07:08', '2025-08-08 02:07:08'),
(42, 1, 'PRODUCT_MGMT_OPENED', 'Product Management opened', NULL, '2025-08-08 02:07:10', '2025-08-08 02:07:10'),
(43, 1, 'PRODUCT_MGMT_OPENED', 'Product Management opened', NULL, '2025-08-08 02:07:26', '2025-08-08 02:07:26'),
(44, 1, 'APPLICATION_EXIT', 'User exited the application', NULL, '2025-08-08 02:07:30', '2025-08-08 02:07:30'),
(45, 1, 'LOGIN', 'Successful login from IP: ************', NULL, '2025-08-08 02:07:50', '2025-08-08 02:07:50'),
(46, 1, 'MAIN_FORM_LOADED', 'Main form loaded successfully', NULL, '2025-08-08 02:07:50', '2025-08-08 02:07:50'),
(47, 1, 'PRODUCT_MGMT_OPENED', 'Product Management opened', NULL, '2025-08-08 02:07:52', '2025-08-08 02:07:52'),
(48, 1, 'APPLICATION_EXIT', 'User exited the application', NULL, '2025-08-08 02:08:07', '2025-08-08 02:08:07');


ALTER TABLE `tbl_addons`
  ADD PRIMARY KEY (`AddonID`);

ALTER TABLE `tbl_cash_drawers`
  ADD PRIMARY KEY (`CashDrawerID`);

ALTER TABLE `tbl_cash_management`
  ADD PRIMARY KEY (`TransactionID`),
  ADD KEY `UserID` (`UserID`);

ALTER TABLE `tbl_categories`
  ADD PRIMARY KEY (`CategoryID`);

ALTER TABLE `tbl_codes`
  ADD PRIMARY KEY (`UNIQUEID`);

ALTER TABLE `tbl_customers`
  ADD PRIMARY KEY (`CustomerID`);

ALTER TABLE `tbl_customer_payments`
  ADD PRIMARY KEY (`PaymentID`),
  ADD KEY `OrderID` (`OrderID`),
  ADD KEY `CustomerID` (`CustomerID`),
  ADD KEY `PaymentMethodID` (`PaymentMethodID`),
  ADD KEY `ReceivedByUserID` (`ReceivedByUserID`);

ALTER TABLE `tbl_expenses`
  ADD PRIMARY KEY (`ExpenseID`),
  ADD KEY `ExpenseTypeID` (`ExpenseTypeID`),
  ADD KEY `UserID` (`UserID`),
  ADD KEY `CashDrawerID` (`CashDrawerID`);

ALTER TABLE `tbl_expense_types`
  ADD PRIMARY KEY (`ExpenseTypeID`);

ALTER TABLE `tbl_ingredients`
  ADD PRIMARY KEY (`IngredientID`);

ALTER TABLE `tbl_inventory`
  ADD PRIMARY KEY (`InventoryID`),
  ADD KEY `IX_Inventory_Product` (`ProductID`);

ALTER TABLE `tbl_orders`
  ADD PRIMARY KEY (`OrderID`),
  ADD KEY `SessionID` (`SessionID`),
  ADD KEY `CustomerID` (`CustomerID`),
  ADD KEY `UserID` (`UserID`),
  ADD KEY `PaymentMethodID` (`PaymentMethodID`),
  ADD KEY `IX_Orders_Date` (`OrderDate`),
  ADD KEY `idx_order_date_user` (`OrderDate`,`UserID`);

ALTER TABLE `tbl_order_addons`
  ADD PRIMARY KEY (`OrderDetailAddonID`),
  ADD KEY `OrderDetailID` (`OrderDetailID`),
  ADD KEY `AddonID` (`AddonID`);

ALTER TABLE `tbl_order_details`
  ADD PRIMARY KEY (`OrderDetailID`),
  ADD KEY `OrderID` (`OrderID`),
  ADD KEY `ProductID` (`ProductID`),
  ADD KEY `ProductSizeID` (`ProductSizeID`),
  ADD KEY `IX_OrderDetails_Status` (`KitchenStatus`);

ALTER TABLE `tbl_payment_methods`
  ADD PRIMARY KEY (`PaymentMethodID`);

ALTER TABLE `tbl_products`
  ADD PRIMARY KEY (`ProductID`),
  ADD KEY `IX_Products_Category` (`CategoryID`);

ALTER TABLE `tbl_product_ingredients`
  ADD PRIMARY KEY (`ProductIngredientID`),
  ADD KEY `ProductID` (`ProductID`),
  ADD KEY `IngredientID` (`IngredientID`);

ALTER TABLE `tbl_product_sizes`
  ADD PRIMARY KEY (`ProductSizeID`),
  ADD KEY `ProductID` (`ProductID`);

ALTER TABLE `tbl_purchases`
  ADD PRIMARY KEY (`PurchaseID`),
  ADD KEY `SupplierID` (`SupplierID`);

ALTER TABLE `tbl_purchase_details`
  ADD PRIMARY KEY (`PurchaseDetailID`),
  ADD KEY `PurchaseID` (`PurchaseID`),
  ADD KEY `ProductID` (`ProductID`);

ALTER TABLE `tbl_purchase_payments`
  ADD PRIMARY KEY (`PaymentID`),
  ADD KEY `PurchaseID` (`PurchaseID`),
  ADD KEY `UserID` (`UserID`);

ALTER TABLE `tbl_roles`
  ADD PRIMARY KEY (`RoleID`);

ALTER TABLE `tbl_role_permissions`
  ADD PRIMARY KEY (`PermissionID`),
  ADD UNIQUE KEY `RoleID` (`RoleID`,`ModuleName`);

ALTER TABLE `tbl_sessions`
  ADD PRIMARY KEY (`SessionID`),
  ADD KEY `TableID` (`TableID`),
  ADD KEY `UserID` (`UserID`),
  ADD KEY `CustomerID` (`CustomerID`),
  ADD KEY `IX_Session_Status` (`IsClosed`);

ALTER TABLE `tbl_stock_movements`
  ADD PRIMARY KEY (`id`),
  ADD KEY `product_id` (`product_id`);

ALTER TABLE `tbl_suppliers`
  ADD PRIMARY KEY (`SupplierID`);

ALTER TABLE `tbl_tables`
  ADD PRIMARY KEY (`TableID`);

ALTER TABLE `tbl_users`
  ADD PRIMARY KEY (`UserID`),
  ADD KEY `RoleID` (`RoleID`);

ALTER TABLE `tbl_user_activities`
  ADD PRIMARY KEY (`ActivityID`),
  ADD KEY `UserID` (`UserID`);


ALTER TABLE `tbl_addons`
  MODIFY `AddonID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;
ALTER TABLE `tbl_cash_drawers`
  MODIFY `CashDrawerID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;
ALTER TABLE `tbl_cash_management`
  MODIFY `TransactionID` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `tbl_categories`
  MODIFY `CategoryID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=362142;
ALTER TABLE `tbl_codes`
  MODIFY `UNIQUEID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;
ALTER TABLE `tbl_customers`
  MODIFY `CustomerID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;
ALTER TABLE `tbl_customer_payments`
  MODIFY `PaymentID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;
ALTER TABLE `tbl_expenses`
  MODIFY `ExpenseID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=49;
ALTER TABLE `tbl_expense_types`
  MODIFY `ExpenseTypeID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;
ALTER TABLE `tbl_ingredients`
  MODIFY `IngredientID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;
ALTER TABLE `tbl_inventory`
  MODIFY `InventoryID` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `tbl_orders`
  MODIFY `OrderID` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `tbl_order_addons`
  MODIFY `OrderDetailAddonID` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `tbl_order_details`
  MODIFY `OrderDetailID` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `tbl_payment_methods`
  MODIFY `PaymentMethodID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;
ALTER TABLE `tbl_products`
  MODIFY `ProductID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;
ALTER TABLE `tbl_product_ingredients`
  MODIFY `ProductIngredientID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;
ALTER TABLE `tbl_product_sizes`
  MODIFY `ProductSizeID` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `tbl_purchases`
  MODIFY `PurchaseID` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `tbl_purchase_details`
  MODIFY `PurchaseDetailID` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `tbl_purchase_payments`
  MODIFY `PaymentID` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `tbl_roles`
  MODIFY `RoleID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;
ALTER TABLE `tbl_role_permissions`
  MODIFY `PermissionID` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `tbl_sessions`
  MODIFY `SessionID` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `tbl_stock_movements`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `tbl_suppliers`
  MODIFY `SupplierID` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `tbl_tables`
  MODIFY `TableID` int(11) NOT NULL AUTO_INCREMENT;
ALTER TABLE `tbl_users`
  MODIFY `UserID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;
ALTER TABLE `tbl_user_activities`
  MODIFY `ActivityID` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=49;

ALTER TABLE `tbl_cash_management`
  ADD CONSTRAINT `tbl_cash_management_ibfk_1` FOREIGN KEY (`UserID`) REFERENCES `tbl_users` (`UserID`);

ALTER TABLE `tbl_customer_payments`
  ADD CONSTRAINT `tbl_customer_payments_ibfk_1` FOREIGN KEY (`OrderID`) REFERENCES `tbl_orders` (`OrderID`),
  ADD CONSTRAINT `tbl_customer_payments_ibfk_2` FOREIGN KEY (`CustomerID`) REFERENCES `tbl_customers` (`CustomerID`),
  ADD CONSTRAINT `tbl_customer_payments_ibfk_3` FOREIGN KEY (`PaymentMethodID`) REFERENCES `tbl_payment_methods` (`PaymentMethodID`),
  ADD CONSTRAINT `tbl_customer_payments_ibfk_4` FOREIGN KEY (`ReceivedByUserID`) REFERENCES `tbl_users` (`UserID`);

ALTER TABLE `tbl_expenses`
  ADD CONSTRAINT `tbl_expenses_ibfk_1` FOREIGN KEY (`ExpenseTypeID`) REFERENCES `tbl_expense_types` (`ExpenseTypeID`),
  ADD CONSTRAINT `tbl_expenses_ibfk_2` FOREIGN KEY (`UserID`) REFERENCES `tbl_users` (`UserID`),
  ADD CONSTRAINT `tbl_expenses_ibfk_3` FOREIGN KEY (`CashDrawerID`) REFERENCES `tbl_cash_drawers` (`CashDrawerID`);

ALTER TABLE `tbl_inventory`
  ADD CONSTRAINT `tbl_inventory_ibfk_1` FOREIGN KEY (`ProductID`) REFERENCES `tbl_products` (`ProductID`);

ALTER TABLE `tbl_orders`
  ADD CONSTRAINT `tbl_orders_ibfk_1` FOREIGN KEY (`SessionID`) REFERENCES `tbl_sessions` (`SessionID`),
  ADD CONSTRAINT `tbl_orders_ibfk_2` FOREIGN KEY (`CustomerID`) REFERENCES `tbl_customers` (`CustomerID`),
  ADD CONSTRAINT `tbl_orders_ibfk_3` FOREIGN KEY (`UserID`) REFERENCES `tbl_users` (`UserID`),
  ADD CONSTRAINT `tbl_orders_ibfk_4` FOREIGN KEY (`PaymentMethodID`) REFERENCES `tbl_payment_methods` (`PaymentMethodID`);

ALTER TABLE `tbl_order_addons`
  ADD CONSTRAINT `tbl_order_addons_ibfk_1` FOREIGN KEY (`OrderDetailID`) REFERENCES `tbl_order_details` (`OrderDetailID`),
  ADD CONSTRAINT `tbl_order_addons_ibfk_2` FOREIGN KEY (`AddonID`) REFERENCES `tbl_addons` (`AddonID`);

ALTER TABLE `tbl_order_details`
  ADD CONSTRAINT `tbl_order_details_ibfk_1` FOREIGN KEY (`OrderID`) REFERENCES `tbl_orders` (`OrderID`),
  ADD CONSTRAINT `tbl_order_details_ibfk_2` FOREIGN KEY (`ProductID`) REFERENCES `tbl_products` (`ProductID`),
  ADD CONSTRAINT `tbl_order_details_ibfk_3` FOREIGN KEY (`ProductSizeID`) REFERENCES `tbl_product_sizes` (`ProductSizeID`);

ALTER TABLE `tbl_products`
  ADD CONSTRAINT `tbl_products_ibfk_1` FOREIGN KEY (`CategoryID`) REFERENCES `tbl_categories` (`CategoryID`);

ALTER TABLE `tbl_product_ingredients`
  ADD CONSTRAINT `tbl_product_ingredients_ibfk_1` FOREIGN KEY (`ProductID`) REFERENCES `tbl_products` (`ProductID`),
  ADD CONSTRAINT `tbl_product_ingredients_ibfk_2` FOREIGN KEY (`IngredientID`) REFERENCES `tbl_ingredients` (`IngredientID`);

ALTER TABLE `tbl_product_sizes`
  ADD CONSTRAINT `tbl_product_sizes_ibfk_1` FOREIGN KEY (`ProductID`) REFERENCES `tbl_products` (`ProductID`);

ALTER TABLE `tbl_purchases`
  ADD CONSTRAINT `tbl_purchases_ibfk_1` FOREIGN KEY (`SupplierID`) REFERENCES `tbl_suppliers` (`SupplierID`);

ALTER TABLE `tbl_purchase_details`
  ADD CONSTRAINT `tbl_purchase_details_ibfk_1` FOREIGN KEY (`PurchaseID`) REFERENCES `tbl_purchases` (`PurchaseID`),
  ADD CONSTRAINT `tbl_purchase_details_ibfk_2` FOREIGN KEY (`ProductID`) REFERENCES `tbl_products` (`ProductID`);

ALTER TABLE `tbl_purchase_payments`
  ADD CONSTRAINT `tbl_purchase_payments_ibfk_1` FOREIGN KEY (`PurchaseID`) REFERENCES `tbl_purchases` (`PurchaseID`) ON DELETE CASCADE,
  ADD CONSTRAINT `tbl_purchase_payments_ibfk_2` FOREIGN KEY (`UserID`) REFERENCES `tbl_users` (`UserID`);

ALTER TABLE `tbl_role_permissions`
  ADD CONSTRAINT `fk_role` FOREIGN KEY (`RoleID`) REFERENCES `tbl_roles` (`RoleID`) ON DELETE CASCADE;

ALTER TABLE `tbl_sessions`
  ADD CONSTRAINT `tbl_sessions_ibfk_1` FOREIGN KEY (`TableID`) REFERENCES `tbl_tables` (`TableID`),
  ADD CONSTRAINT `tbl_sessions_ibfk_2` FOREIGN KEY (`UserID`) REFERENCES `tbl_users` (`UserID`),
  ADD CONSTRAINT `tbl_sessions_ibfk_3` FOREIGN KEY (`CustomerID`) REFERENCES `tbl_customers` (`CustomerID`);

ALTER TABLE `tbl_stock_movements`
  ADD CONSTRAINT `tbl_stock_movements_ibfk_1` FOREIGN KEY (`product_id`) REFERENCES `tbl_products` (`ProductID`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `tbl_users`
  ADD CONSTRAINT `tbl_users_ibfk_1` FOREIGN KEY (`RoleID`) REFERENCES `tbl_roles` (`RoleID`);

ALTER TABLE `tbl_user_activities`
  ADD CONSTRAINT `tbl_user_activities_ibfk_1` FOREIGN KEY (`UserID`) REFERENCES `tbl_users` (`UserID`);